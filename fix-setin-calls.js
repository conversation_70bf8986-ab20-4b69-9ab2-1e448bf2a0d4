const fs = require('fs');
const path = require('path');

// 读取文件内容
const filePath = 'app/3x-ui/(modal)/inbound-config.tsx';
let content = fs.readFileSync(filePath, 'utf8');

// 定义需要替换的模式
const patterns = [
  // 基本的 setIn 调用模式
  {
    pattern: /(\w+Settings)\(setIn\((\w+Settings),/g,
    replacement: '$1(prev => setIn(prev,'
  },
  {
    pattern: /(\w+Form)\(setIn\((\w+Form),/g,
    replacement: '$1(prev => setIn(prev,'
  },
  // 特殊情况
  {
    pattern: /setSniffingForm\(setIn\(sniffingForm,/g,
    replacement: 'setSniffingForm(prev => setIn(prev,'
  },
  {
    pattern: /setTlsForm\(setIn\(tlsForm,/g,
    replacement: 'setTlsForm(prev => setIn(prev,'
  },
  {
    pattern: /setRealityForm\(setIn\(realityForm,/g,
    replacement: 'setRealityForm(prev => setIn(prev,'
  },
  {
    pattern: /setSockOptForm\(setIn\(sockOptForm,/g,
    replacement: 'setSockOptForm(prev => setIn(prev,'
  },
  {
    pattern: /setTcpForm\(setIn\(tcpForm,/g,
    replacement: 'setTcpForm(prev => setIn(prev,'
  },
  {
    pattern: /setXhttpForm\(setIn\(xhttpForm,/g,
    replacement: 'setXhttpForm(prev => setIn(prev,'
  },
  {
    pattern: /setMkcpForm\(setIn\(mkcpForm,/g,
    replacement: 'setMkcpForm(prev => setIn(prev,'
  },
  {
    pattern: /setGrpcForm\(setIn\(grpcForm,/g,
    replacement: 'setGrpcForm(prev => setIn(prev,'
  },
  {
    pattern: /setWsForm\(setIn\(wsForm,/g,
    replacement: 'setWsForm(prev => setIn(prev,'
  },
  {
    pattern: /setHttpUpgradeForm\(setIn\(httpUpgradeForm,/g,
    replacement: 'setHttpUpgradeForm(prev => setIn(prev,'
  },
  {
    pattern: /setWireguardSettings\(setIn\(wireguardSettings,/g,
    replacement: 'setWireguardSettings(prev => setIn(prev,'
  },
  {
    pattern: /setDokodemoSettings\(setIn\(dokodemoSettings,/g,
    replacement: 'setDokodemoSettings(prev => setIn(prev,'
  }
];

// 应用所有替换
patterns.forEach(({ pattern, replacement }) => {
  content = content.replace(pattern, replacement);
});

// 写回文件
fs.writeFileSync(filePath, content, 'utf8');

console.log('已完成 setIn 调用的函数式更新替换');
